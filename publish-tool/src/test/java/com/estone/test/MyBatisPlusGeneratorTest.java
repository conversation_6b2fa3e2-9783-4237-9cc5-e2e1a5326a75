package com.estone.test;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-07-06 上午11:59
 */
public class MyBatisPlusGeneratorTest {

    public static void main(String[] args) {
        // 刊登系统数据源
        generatorPublishTIDB();
        // 外部系统数据源
//        generatorExternalSystems();

    }

    private static void generatorPublishTIDB() {
        // 项目路径 projectPath
        String userDir = System.getProperty("user.dir");
        // 项目名称
        String project = "\\publish-amazon";
        String projectPath = userDir + project;

        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setOutputDir(projectPath + "/src/main/java") // 设置输出目录
                .setAuthor("phc") // 设置作者
                .setOpen(false) // 设置生成后是否自动打开目录
                .setFileOverride(true) // 设置文件存在时是否覆盖
                .setServiceName("%sService") // 设置Service接口名后缀
                .setIdType(IdType.AUTO) // 设置主键生成策略
                .setSwagger2(false); // 设置是否生成Swagger注解

        // 数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDbType(DbType.MYSQL) // 设置数据库类型
                .setUrl("*******************************************************************************") // 数据库连接URL
                .setUsername("tidb") // 数据库用户名
                .setPassword("!QAZxsw2") // 数据库密码
                .setDriverName("com.mysql.cj.jdbc.Driver"); // 数据库驱动类名

        // 策略配置
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setInclude("amazon_trademark_word_whitelist") // 指定需要生成代码的表名
                .setNaming(NamingStrategy.underline_to_camel) // 设置表名转类名策略
                .setColumnNaming(NamingStrategy.underline_to_camel) // 设置列名转属性名策略
                .setEntityLombokModel(true) // 设置实体类使用Lombok模型
                .setRestControllerStyle(true) // 设置Controller使用REST风格
                .setTablePrefix(""); // 设置表名前缀

        // 包配置
        PackageConfig packageConfig = new PackageConfig();
        packageConfig.setParent("com.estone.erp.publish.tidb.publishtidb") // 设置父包名
                .setMapper("mapper") // 设置Mapper接口所在的子包名
                .setEntity("model") // 设置实体类所在的子包名
                .setController("controller") // 设置Controller所在的子包名
                .setService("service")
                .setXml("mapper"); // 设置Mapper XML文件所在的子包名


        // 模板配置
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setXml(null) // 不生成XML文件
                .setController("templates/tidbController.java") // 设置Controller模板路径
                .setEntity("templates/entity.java") // 设置实体类模板路径
                .setService("templates/service.java") // 设置实体类模板路径
                .setMapper("templates/mapper.java") // 设置Mapper接口模板路径
                .setXml("templates/mapper.xml");

        // 整合配置
        AutoGenerator autoGenerator = new AutoGenerator();
        autoGenerator.setGlobalConfig(globalConfig)
                .setDataSource(dataSourceConfig)
                .setStrategy(strategyConfig)
                .setPackageInfo(packageConfig)
                .setTemplateEngine(new FreemarkerTemplateEngine())
                .setTemplate(templateConfig);

        // 执行生成
        autoGenerator.execute();
    }


    private static void generatorExternalSystems() {

        // 项目路径 projectPath
        String userDir = System.getProperty("user.dir");
        // 项目名称
        String project = "\\publish-common";
        String projectPath = userDir + project;

        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setOutputDir(projectPath + "/src/main/java") // 设置输出目录
                .setAuthor("xhx") // 设置作者
                .setOpen(false) // 设置生成后是否自动打开目录
                .setFileOverride(true) // 设置文件存在时是否覆盖
                .setServiceName("%sService") // 设置Service接口名后缀
                .setIdType(IdType.AUTO) // 设置主键生成策略
                .setSwagger2(false); // 设置是否生成Swagger注解

        // 数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDbType(DbType.MYSQL) // 设置数据库类型
                .setUrl("*********************************************************************************") // 数据库连接URL
                .setUsername("tidb") // 数据库用户名
                .setPassword("!QAZxsw2") // 数据库密码
                .setDriverName("com.mysql.cj.jdbc.Driver"); // 数据库驱动类名

        // 策略配置
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setInclude("rpa_amazon_account_health_detail_result", "rpa_amazon_account_health_result") // 指定需要生成代码的表名
                .setNaming(NamingStrategy.underline_to_camel) // 设置表名转类名策略
                .setColumnNaming(NamingStrategy.underline_to_camel) // 设置列名转属性名策略
                .setEntityLombokModel(true) // 设置实体类使用Lombok模型
                .setRestControllerStyle(true) // 设置Controller使用REST风格
                .setTablePrefix(""); // 设置表名前缀

        // 包配置
        PackageConfig packageConfig = new PackageConfig();
        packageConfig.setParent("com.estone.erp.publish.tidb.powerautomate") // 设置父包名
                .setMapper("mapper") // 设置Mapper接口所在的子包名
                .setEntity("entity") // 设置实体类所在的子包名
                .setController("controller") // 设置Controller所在的子包名
                .setService("service")
                .setXml("mapper"); // 设置Mapper XML文件所在的子包名

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                Map<String, Object> map = new HashMap<>();
                // 自定义配置，在模板中可以通过 cfg.daoPackage 获取
                map.put("daoPackage", packageConfig.getParent() + ".dao");
                this.setMap(map);
            }
        };

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        // 添加Dao接口模板
        focList.add(new FileOutConfig("/templates/dao.java.ftl") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/java/" +
                        packageConfig.getParent().replace(".", "/") +
                        "/dao/" + tableInfo.getEntityName() + "Dao.java";
            }
        });
        // 添加DaoImpl实现类模板
        focList.add(new FileOutConfig("/templates/daoImpl.java.ftl") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/java/" +
                        packageConfig.getParent().replace(".", "/") +
                        "/dao/impl/" + tableInfo.getEntityName() + "DaoImpl.java";
            }
        });

        cfg.setFileOutConfigList(focList);

        // 模板配置
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setXml(null) // 不生成XML文件
//                .setController("templates/tidbController.java") // 设置Controller模板路径
                .setEntity("templates/entity.java") // 设置实体类模板路径
                .setMapper("templates/mapper.java"); // 设置Mapper接口模板路径

        // 整合配置
        AutoGenerator autoGenerator = new AutoGenerator();
        autoGenerator.setGlobalConfig(globalConfig)
                .setDataSource(dataSourceConfig)
                .setStrategy(strategyConfig)
                .setPackageInfo(packageConfig)
                .setTemplateEngine(new FreemarkerTemplateEngine())
                .setTemplate(templateConfig)
                .setCfg(cfg);

        // 执行生成
        autoGenerator.execute();
    }
}
