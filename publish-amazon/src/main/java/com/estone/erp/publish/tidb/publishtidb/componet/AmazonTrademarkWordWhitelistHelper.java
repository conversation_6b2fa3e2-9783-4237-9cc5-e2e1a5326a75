package com.estone.erp.publish.tidb.publishtidb.componet;

import com.alibaba.nacos.common.utils.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.util.Triple;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AmazonTrademarkWordWhitelistHelper {


    //TODO Mark:查询商标此
    public Map<String, Triple<String, String, String>> getInfringementWordDetailMap(List<String> infringementWords) {


        return null;
    }


    public void addWhitelists(Map<String, List<String>> whitelistMap) {
        if (MapUtil.isEmpty(whitelistMap)) {
            return;
        }


        List<String> infringementWords = new ArrayList<>(whitelistMap.keySet());

        Map<String, Triple<String, String, String>> infringementWordDetailMap = getInfringementWordDetailMap(infringementWords);


        for (Map.Entry<String, List<String>> entry : whitelistMap.entrySet()) {
            String infringementWord = entry.getKey();

            List<String> siteList = entry.getValue();
            if (CollectionUtils.isNotEmpty(siteList)) {
                // 如果站点存在为空的情况，则直接将站点列表设置为null，以便后续作为全站点的判断依据
                boolean isExistBlank = siteList.stream().anyMatch(StringUtils::isBlank);
                if (isExistBlank) {
                    siteList = null;
                }
            }


            Triple<String, String, String> infringementWordDetail = infringementWordDetailMap.get(infringementWord);
            if (infringementWordDetail == null) {
                log.error("侵权词{}不存在", infringementWord);
                continue;
            }


        }


    }
}
