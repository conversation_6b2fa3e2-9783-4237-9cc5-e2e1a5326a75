package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.tidb.publishtidb.domain.AmazonTrademarkWordWhitelistVO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonTrademarkWordWhitelistMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonWordDeleteLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonWordDeleteLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * Amazon商标词白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Service
public class AmazonTrademarkWordWhitelistServiceImpl extends ServiceImpl<AmazonTrademarkWordWhitelistMapper, AmazonTrademarkWordWhitelist> implements AmazonTrademarkWordWhitelistService {

    @Resource
    private AmazonWordDeleteLogService amazonWordDeleteLogService;

    /**
     * 构建查询条件
     *
     * @param dto 查询条件DTO
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<AmazonTrademarkWordWhitelist> buildQueryWrapper(TrademarkWordWhitelistQueryDTO dto) {
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = new LambdaQueryWrapper<>();

        // 站点查询
        wrapper.eq(StringUtils.isNotBlank(dto.getSite()), AmazonTrademarkWordWhitelist::getSite, dto.getSite());

        // 侵权词汇模糊查询
        wrapper.like(StringUtils.isNotBlank(dto.getInfringementWord()),
            AmazonTrademarkWordWhitelist::getInfringementWord, dto.getInfringementWord());


        // 商标词标识
        if(CollectionUtils.isNotEmpty(dto.getTrademarkIdentificationList())){
            wrapper.and(wr ->{
                for (String trademark : dto.getTrademarkIdentificationList()) {
                    wr.or().like(AmazonTrademarkWordWhitelist::getTrademarkIdentification, trademark);
                }
                return wr;
            });
        }

        // 禁售平台查询
        if (CollectionUtils.isNotEmpty(dto.getForbidChannelList())) {
            List<String> forbidChannels = dto.getForbidChannelList();
            wrapper.and(wr -> {
                for (String channel : forbidChannels) {
                    wr.or().like(AmazonTrademarkWordWhitelist::getForbidChannel, channel);
                }
                return wr;
            });
        }

        // 禁售站点查询
        if (CollectionUtils.isNotEmpty(dto.getProhibitionSiteList())) {
            List<String> prohibitionSites = dto.getProhibitionSiteList();
            List<String> forbidChannels = dto.getForbidChannelList();

            wrapper.and(wr -> {
                for (String site : prohibitionSites) {
                    if (CollectionUtils.isNotEmpty(forbidChannels)) {
                        for (String channel : forbidChannels) {
                            String keyword = channel + "_" + site;
                            wr.or().like(AmazonTrademarkWordWhitelist::getProhibitionSite, keyword);
                        }
                    } else {
                        wr.or().like(AmazonTrademarkWordWhitelist::getProhibitionSite, site);
                    }
                }
                return wr;
            });
        }

        // 添加人查询
        wrapper.in(CollectionUtils.isNotEmpty(dto.getCreateByList()), AmazonTrademarkWordWhitelist::getCreateBy, dto.getCreateByList());

        // 添加时间范围查询
        wrapper.ge(StringUtils.isNotBlank(dto.getCreatedTimeStart() ), AmazonTrademarkWordWhitelist::getCreatedTime, dto.getCreatedTimeStart());
        wrapper.le(StringUtils.isNotBlank(dto.getCreatedTimeEnd()), AmazonTrademarkWordWhitelist::getCreatedTime, dto.getCreatedTimeEnd());

        // 修改时间范围查询
        wrapper.ge(StringUtils.isNotBlank(dto.getModifiedTimeStart()) , AmazonTrademarkWordWhitelist::getModifiedTime, dto.getModifiedTimeStart());
        wrapper.le(StringUtils.isNotBlank(dto.getModifiedTimeEnd()), AmazonTrademarkWordWhitelist::getModifiedTime, dto.getModifiedTimeEnd());

        // 更新时间范围查询
        wrapper.ge(StringUtils.isNotBlank(dto.getUpdatedTimeStart()) , AmazonTrademarkWordWhitelist::getUpdatedTime, dto.getUpdatedTimeStart());
        wrapper.le(StringUtils.isNotBlank(dto.getUpdatedTimeEnd()), AmazonTrademarkWordWhitelist::getUpdatedTime, dto.getUpdatedTimeEnd());

        wrapper.orderByDesc(AmazonTrademarkWordWhitelist::getCreatedTime);
        return wrapper;
}

    @Override
    public CQueryResult<AmazonTrademarkWordWhitelistVO> queryPage(CQuery<TrademarkWordWhitelistQueryDTO> query) {
        // 获取查询条件
        TrademarkWordWhitelistQueryDTO dto = query.getSearch();

        // 构建查询条件
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = buildQueryWrapper(dto);


        // 分页查询
        IPage<AmazonTrademarkWordWhitelist> resultPage = baseMapper.selectPage(new Page<>(query.getPage(), query.getLimit()), wrapper);

        // 转换为响应对象
        List<AmazonTrademarkWordWhitelistVO> responseList = resultPage.getRecords().stream()
                .map(AmazonTrademarkWordWhitelistVO::fromEntity)
                .collect(Collectors.toList());

        // 构建CQueryResult响应
        CQueryResult<AmazonTrademarkWordWhitelistVO> result = new CQueryResult<>();
        result.setRows(responseList);
        result.setSuccess(true);
        result.setTotal(resultPage.getTotal());
        result.setTotalPages(Long.valueOf(resultPage.getPages()).intValue());

        return result;

    }

    @Override
    public void addWhitelist(TrademarkWordWhitelistSaveDTO dto) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if(StringUtils.isBlank(dto.getInfringementWord())){
            return;
        }
        // 先删除再新增

        if(StringUtils.isBlank(dto.getSite())){
            LambdaQueryWrapper<AmazonTrademarkWordWhitelist> whitelistWr = new LambdaQueryWrapper<>();
            whitelistWr.eq(AmazonTrademarkWordWhitelist::getInfringementWord,dto.getInfringementWord());
            List<AmazonTrademarkWordWhitelist> whitelists = list(whitelistWr);

            for (AmazonTrademarkWordWhitelist whitelist : whitelists) {
                removeById(whitelist.getId());
                AmazonWordDeleteLog deleteLog  = new AmazonWordDeleteLog();
                deleteLog.setSite(whitelist.getSite());
                deleteLog.setInfringementWord(whitelist.getInfringementWord());
                deleteLog.setOperatedTime(LocalDateTime.now());
                deleteLog.setOperatedBy(currentUser);
                amazonWordDeleteLogService.save(deleteLog);
            }
        }
    }
}
