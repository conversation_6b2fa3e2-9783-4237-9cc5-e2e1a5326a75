package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * amazon词汇删除日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_word_delete_log")
public class AmazonWordDeleteLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇
     */
    private String infringementWord;

    /**
     * 操作人
     */
    private String operatedBy;

    /**
     * 操作时间
     */
    private LocalDateTime operatedTime;


}
