package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.WordDeleteLogQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonWordDeleteLogMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonWordDeleteLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonWordDeleteLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * amazon词汇删除日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Service
public class AmazonWordDeleteLogServiceImpl extends ServiceImpl<AmazonWordDeleteLogMapper, AmazonWordDeleteLog> implements AmazonWordDeleteLogService {

    @Override
    public CQueryResult<AmazonWordDeleteLog> queryPage(CQuery<WordDeleteLogQueryDTO> query) {

        WordDeleteLogQueryDTO search = query.getSearch();

        LambdaQueryWrapper<AmazonWordDeleteLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollectionUtils.isNotEmpty(search.getOperatedByList()), AmazonWordDeleteLog::getOperatedBy, search.getOperatedByList());

        // 分页查询
        IPage<AmazonWordDeleteLog> resultPage = baseMapper.selectPage(new Page<>(query.getPage(), query.getLimit()), wrapper);

        // 构建CQueryResult响应
        CQueryResult<AmazonWordDeleteLog> result = new CQueryResult<>();
        result.setRows(resultPage.getRecords());
        result.setSuccess(true);
        result.setTotal(resultPage.getTotal());
        result.setTotalPages(Long.valueOf(resultPage.getPages()).intValue());

        return result;
    }
}
