package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.WordDeleteLogQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonWordDeleteLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonWordDeleteLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
* <p>
* amazon词汇删除日志 前端控制器
* </p>
*
* <AUTHOR>
* @since 2025-07-13
*/
@Slf4j
@RestController
@RequestMapping("/amazonWordDeleteLog")
public class AmazonWordDeleteLogController {
    @Resource
    private AmazonWordDeleteLogService amazonWordDeleteLogService;




    /**
     * 查询Amazon词汇删除日志
     *
     * @param query 查询参数
     * @return 分页查询结果
     */
    @PostMapping("/queryPage")
    public CQueryResult<AmazonWordDeleteLog> queryPage(@RequestBody(required = true) CQuery<WordDeleteLogQueryDTO> query) {
        try {
            return amazonWordDeleteLogService.queryPage(query);
        } catch (Exception e) {
            log.error("查询Amazon词汇删除日志异常", e);
            return CQueryResult.failResult(e.getMessage());
        }
    }


}
