package com.estone.erp.publish.tidb.publishtidb.domain;

import lombok.Data;

import java.util.List;

@Data
public class TrademarkWordWhitelistSaveDTO {
    /**
     * 站点
     */
    private List<String> site;

    /**
     * 侵权词汇
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private String trademarkIdentification;

    /**
     * 禁售站点
     */
    private String prohibitionSite;

    /**
     * 禁售平台
     */
    private String forbidChannel;

}
